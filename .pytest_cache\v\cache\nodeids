["test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_logs_clear", "test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_logs_download", "test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_logs_empty", "test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_logs_with_data", "test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_status_not_running", "test_scraper.py::TestFlaskAPIEndpoints::test_api_scraping_status_running", "test_scraper.py::TestScrapingLogger::test_clear_logs", "test_scraper.py::TestScrapingLogger::test_different_log_levels", "test_scraper.py::TestScrapingLogger::test_log_creation", "test_scraper.py::TestScrapingLogger::test_log_export", "test_scraper.py::TestScrapingLogger::test_log_formatting", "test_scraper.py::TestScrapingLogger::test_logger_initialization", "test_scraper.py::TestScrapingLogger::test_thread_safety"]